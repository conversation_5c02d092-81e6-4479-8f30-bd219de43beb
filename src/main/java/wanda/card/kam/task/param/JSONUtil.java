package wanda.card.kam.task.param;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by fuwei on 2021/6/28.
 */
public class JSONUtil {
    private static List<SerializerFeature> serializerFeatureList = Lists.newArrayList(
            SerializerFeature.WriteNullListAsEmpty,
            SerializerFeature.WriteNullStringAsEmpty,
            SerializerFeature.WriteNullNumberAsZero,
            SerializerFeature.WriteNullBooleanAsFalse
    );


    private JSONUtil() {
    }

    public static String toJsonStr(Object object) {
        return toJsonStr(object, false);
    }

    public static String toJsonStr(Object object, boolean format) {
        if (format)
            serializerFeatureList.add(SerializerFeature.PrettyFormat);
        return JSON.toJSONString(object, serializerFeatureList.toArray(new SerializerFeature[serializerFeatureList.size()]));
    }

}