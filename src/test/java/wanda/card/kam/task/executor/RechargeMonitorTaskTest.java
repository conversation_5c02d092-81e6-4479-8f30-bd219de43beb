package wanda.card.kam.task.executor;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.task.TaskApplication;

/**
 * 充值监控任务测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = {TaskApplication.class})
class RechargeMonitorTaskTest {
    @Autowired
    private RechargeMonitorTask rechargeMonitorTask;

    @Test
    void testExecuteSuccess() {
        rechargeMonitorTask
                .execute("{\"timeThreshold\": 30, \"alarmEmail\": \"<EMAIL>\", \"resendTopic\": \"\"}");
    }
}