package wanda.card.kam.admin.api.controller;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.api.model.Result;
import wanda.stark.core.codec.JsonUtils;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/")
public class MockController {

    @PostMapping(value = "/notify/recharge/result")
    public Result<Void> receive(@RequestBody NotifyRechargeResultRequest request) {
        log.info("财管接收充值结构 request:{}", JsonUtils.encode(request));
        return Result.success();
    }

    @Getter
    @Setter
    public static class NotifyRechargeResultRequest {

        /**
         * 充值请求流水号(批次号)
         */
        private String batchNo;

        /**
         * 合同号
         */
        private String contractNo;

        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 每张卡充值金额（分）
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额（分）
         */
        private Integer everyCardPresentAmount;

        /**
         * 卡号列表
         */
        private List<CardInfo> cardInfos;

        /**
         * 卡信息
         */
        @Data
        public static class CardInfo {
            /**
             * 卡号
             */
            private String cardNo;

            /**
             * true为充值成功，false为充值失败
             */
            private boolean rechargeResult;

            /**
             * 失败信息
             */
            private String rechargeFailResult;
        }
    }
}