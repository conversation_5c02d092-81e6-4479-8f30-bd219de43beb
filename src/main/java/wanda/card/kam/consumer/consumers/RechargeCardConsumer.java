package wanda.card.kam.consumer.consumers;

import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import wanda.card.kam.consumer.service.RechargeOrderConsumerService;
import wanda.stark.msg.subscriber.AbstractConsumer;
import wanda.stark.msg.subscriber.MessageConsumer;

import static wanda.card.kam.common.contract.constant.Topic.RECHARGE_CARD_TOPIC;

/**
 * 充值订单消费者
 * 处理批次充值消息
 */
@MessageConsumer(topic = RECHARGE_CARD_TOPIC)
public class RechargeCardConsumer extends AbstractConsumer<String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RechargeCardConsumer.class);

    @Autowired
    private RechargeOrderConsumerService rechargeOrderConsumerService;

    @Override
    public void process(String batchNo, MessageExt origin) {
        LOGGER.info("接收到充值订单消息，批次号：{}", batchNo);

        try {
            // 处理批次充值
            rechargeOrderConsumerService.processBatchRecharge(batchNo);
            LOGGER.info("批次充值处理完成，批次号：{}", batchNo);

        } catch (Exception e) {
            LOGGER.error("处理批次充值失败，批次号：{}", batchNo, e);
            throw e; // 重新抛出异常，让消息队列进行重试
        }
    }
}