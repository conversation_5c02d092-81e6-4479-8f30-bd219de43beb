package wanda.card.kam.consumer.service.biz;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卡充值结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardRechargeResult {

    /**
     * 充值是否成功
     */
    private boolean success;

    /**
     * 错误信息（充值失败时填充）
     */
    private String errorMsg;
    /**
     * 充值金额（分）
     */
    private Integer rechargeAmount;

    /**
     * 赠送金额（分）
     */
    private Integer presentAmount;

    /**
     * 余额变动（充值金额+赠送金额）（分）
     */
    private Integer changeBalance;

    /**
     * 充值前余额（分）
     */
    private Integer beforeBalance;

    /**
     * 充值后余额（分）
     */
    private Integer afterBalance;

    public static CardRechargeResult success(int rechargeAmount, int presentAmount, int afterBalance, int beforeBalance, int changeBalance) {
        return new CardRechargeResult(true, null, rechargeAmount, presentAmount, afterBalance, beforeBalance, changeBalance);
    }

    public static CardRechargeResult failed(String errorMsg) {
        return new CardRechargeResult(false, errorMsg, 0, 0, 0, 0, 0);
    }
}